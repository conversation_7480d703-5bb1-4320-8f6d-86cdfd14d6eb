<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { accountDeletionSchema, type AccountDeletionSchema } from '$lib/schemas/accountDeletion';
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Form from '$lib/components/ui/form';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { AlertTriangle, Trash2 } from 'lucide-svelte';
	import toast from 'svelte-5-french-toast';

	interface Props {
		open: boolean;
		onOpenChange: (open: boolean) => void;
		onConfirm: (data: { reason?: string }) => Promise<void>;
	}

	let { open = $bindable(), onOpenChange, onConfirm }: Props = $props();

	const form = superForm(
		{ reason: '', confirmText: '', acknowledgment: false },
		{
			validators: zodClient(accountDeletionSchema),
			onSubmit: async ({ formData }) => {
				const reason = formData.get('reason') as string;
				await onConfirm({ reason: reason || undefined });
				open = false;
			},
			onError: () => {
				toast.error('Please check the form and try again');
			}
		}
	);

	const { form: formData, enhance, errors, submitting } = form;

	// Reset form when dialog closes
	$effect(() => {
		if (!open) {
			$formData = { reason: '', confirmText: '', acknowledgment: false };
		}
	});
</script>

<Dialog.Root {open} {onOpenChange}>
	<Dialog.Content class="sm:max-w-md">
		<Dialog.Header>
			<Dialog.Title class="flex items-center gap-2 text-red-900">
				<AlertTriangle class="h-5 w-5" />
				Delete Account
			</Dialog.Title>
			<Dialog.Description class="text-sm text-muted-foreground">
				This action is permanent and cannot be undone. All your data will be removed.
			</Dialog.Description>
		</Dialog.Header>

		<form method="POST" use:enhance class="space-y-4">
			<Form.Field {form} name="reason">
				<Form.Control let:attrs>
					<Form.Label class="text-sm font-medium">Reason (optional)</Form.Label>
					<Textarea
						{...attrs}
						bind:value={$formData.reason}
						placeholder="Help us improve by sharing why you're leaving..."
						class="min-h-[80px] resize-none"
					/>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="confirmText">
				<Form.Control let:attrs>
					<Form.Label class="text-sm font-medium">
						Type <span class="font-mono font-bold text-red-600">DELETE</span> to confirm
					</Form.Label>
					<Input
						{...attrs}
						bind:value={$formData.confirmText}
						placeholder="DELETE"
						class="font-mono"
					/>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Form.Field {form} name="acknowledgment" class="flex items-center space-x-2">
				<Form.Control let:attrs>
					<Checkbox {...attrs} bind:checked={$formData.acknowledgment} />
					<Form.Label class="text-sm text-muted-foreground leading-tight">
						I understand this action is permanent and will delete all my account data
					</Form.Label>
				</Form.Control>
				<Form.FieldErrors />
			</Form.Field>

			<Dialog.Footer class="flex justify-end gap-2 pt-4">
				<Dialog.Close asChild let:builder>
					<Button builders={[builder]} variant="outline" size="sm">Cancel</Button>
				</Dialog.Close>
				<Button
					type="submit"
					variant="destructive"
					size="sm"
					disabled={$submitting || $formData.confirmText !== 'DELETE' || !$formData.acknowledgment}
					class="min-w-[100px]"
				>
					{#if $submitting}
						<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
						Deleting...
					{:else}
						<Trash2 class="h-4 w-4 mr-2" />
						Delete Account
					{/if}
				</Button>
			</Dialog.Footer>
		</form>
	</Dialog.Content>
</Dialog.Root>
