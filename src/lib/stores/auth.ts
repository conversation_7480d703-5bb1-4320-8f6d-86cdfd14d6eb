import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import { nhost, recreateNhostClient } from './nhost.js';
import type { NhostSession } from '@nhost/nhost-js';
import type { UserProfile, AuthResult, SignUpOptions } from '$lib/types/auth.js';

// Extended types for better type safety
interface NhostError {
  message: string;
  status?: number;
  code?: string;
}

interface DeviceInfo {
  userAgent: string;
  language: string;
  timestamp: string;
}

interface RequestOptions {
  headers?: Record<string, string>;
}

// Session management flags
let isSigningOut = false;
let authStateListenerActive = true;

// Enhanced session management configuration
const SESSION_CONFIG = {
  IDLE_TIMEOUT: 30 * 60 * 1000, // 30 minutes in milliseconds
  WARNING_TIME: 5 * 60 * 1000,  // Show warning 5 minutes before timeout
  ACTIVITY_EVENTS: ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
};

// Session state stores
export const session = writable<NhostSession | null>(null);
export const userProfile = writable<UserProfile | null>(null);
export const sessionWarning = writable<boolean>(false);
export const sessionTimeRemaining = writable<number>(SESSION_CONFIG.IDLE_TIMEOUT);

// Derived stores
export const isAuthenticated = derived(session, ($session) => {
  return $session?.user && !$session?.user?.isAnonymous;
});

export const userRole = derived(session, ($session) => {
  if (!$session?.user) return null;
  return $session.user.defaultRole || 'user';
});

export const userRoles = derived(session, ($session) => {
  if (!$session?.user) return [];
  // NhostSession.user.roles is string[] according to the nhost types
  const roles = $session.user.roles || [];
  return roles;
});

// Session management class
class SessionManager {
  private idleTimer: number | null = null;
  private warningTimer: number | null = null;
  private lastActivity: number = Date.now();
  private warningShown: boolean = false;

  constructor() {
    if (!browser) return;
    
    this.setupActivityListeners();
    this.startTimers();
  }

  private setupActivityListeners() {
    if (!browser || typeof document === 'undefined') return;
    
    SESSION_CONFIG.ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, () => this.updateActivity(), { passive: true });
    });
  }

  private updateActivity() {
    this.lastActivity = Date.now();

    if (this.warningShown) {
      this.hideWarning();
    }

    this.resetTimers();

    // Log activity for debugging if needed
    console.debug('User activity detected at:', new Date(this.lastActivity).toISOString());
  }

  private startTimers() {
    this.resetTimers();
  }

  private resetTimers() {
    // Clear existing timers
    if (this.idleTimer) clearTimeout(this.idleTimer);
    if (this.warningTimer) clearTimeout(this.warningTimer);
    
    // Start warning timer
    this.warningTimer = window.setTimeout(() => {
      this.showWarning();
    }, SESSION_CONFIG.IDLE_TIMEOUT - SESSION_CONFIG.WARNING_TIME);
    
    // Start idle timeout timer
    this.idleTimer = window.setTimeout(() => {
      this.handleIdleTimeout();
    }, SESSION_CONFIG.IDLE_TIMEOUT);
    
    // Update remaining time
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
  }

  private showWarning() {
    this.warningShown = true;
    sessionWarning.set(true);
    
    // Start countdown
    const startTime = Date.now();
    const countdownInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = SESSION_CONFIG.WARNING_TIME - elapsed;
      
      if (remaining <= 0) {
        clearInterval(countdownInterval);
        return;
      }
      
      sessionTimeRemaining.set(remaining);
    }, 1000);
  }

  private hideWarning() {
    this.warningShown = false;
    sessionWarning.set(false);
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
  }

  private async handleIdleTimeout() {
    console.log('Session idle timeout - signing out');
    await authActions.signOut();
  }

  public extendSession() {
    this.updateActivity();
  }

  public destroy() {
    if (!browser) return;
    
    if (this.idleTimer) clearTimeout(this.idleTimer);
    if (this.warningTimer) clearTimeout(this.warningTimer);
    
    if (typeof document !== 'undefined') {
      SESSION_CONFIG.ACTIVITY_EVENTS.forEach(event => {
        document.removeEventListener(event, () => this.updateActivity());
      });
    }
  }
}

// Initialize session manager
let sessionManager: SessionManager | null = null;

// Browser initialization
if (browser) {
  const initializeAuth = async () => {
    try {
      console.log('🔄 INITIALIZING AUTH SYSTEM...');
      
      // SECURITY: Always start with completely clean state
      isSigningOut = false;
      authStateListenerActive = true;
      session.set(null);
      userProfile.set(null);
      
      // AGGRESSIVE: Clear any residual storage on startup
      if (browser) {
        try {
          // Don't clear everything, but clear auth-specific items
          const authKeys = Object.keys(localStorage).filter(key => 
            key.includes('nhost') || 
            key.includes('auth') || 
            key.includes('session') ||
            key.includes('token')
          );
          
          authKeys.forEach(key => {
            console.log('🧹 Removing suspicious localStorage key:', key);
            localStorage.removeItem(key);
          });
          
          // Clear sessionStorage completely (it's session-specific anyway)
          sessionStorage.clear();
          
        } catch (error) {
          console.error('❌ Initial storage clear error:', error);
        }
      }
      
      // CRITICAL: Check for any existing session AFTER clearing storage
      const initialSession = nhost.auth.getSession();
      console.log('🔍 Initial session check:', initialSession?.user?.email || 'No session');
      
      if (initialSession?.user) {
        console.log('⚠️ Found existing session, validating with server...');
        
        try {
          // CRITICAL: Verify session is still valid with server
          await nhost.auth.refreshSession();
          const validatedSession = nhost.auth.getSession();
          
          if (validatedSession?.user && validatedSession.user.id === initialSession.user.id) {
            console.log('✅ Session validation successful for user:', validatedSession.user.email);
            session.set(validatedSession);
            sessionManager = new SessionManager();
          } else {
            console.log('❌ Session validation failed - user mismatch or invalid session');
            await authActions.forceSessionReset();
          }
        } catch (error) {
          console.error('❌ Session validation failed:', error);
          await authActions.forceSessionReset();
        }
      } else {
        console.log('✅ No initial session found - starting fresh');
      }
      
      console.log('✅ Auth initialization completed');
    } catch (error) {
      console.error('❌ Auth initialization error:', error);
      session.set(null);
      userProfile.set(null);
    }
  };

  // Listen to auth state changes with strict control
  nhost.auth.onAuthStateChanged((event: string, nhostSession: NhostSession | null) => {
    // CRITICAL: Ignore auth state changes during logout process
    if (isSigningOut) {
      console.log('IGNORING auth state change during logout:', event);
      return;
    }
    
    // CRITICAL: Ignore if auth listener is disabled
    if (!authStateListenerActive) {
      console.log('IGNORING auth state change - listener disabled:', event);
      return;
    }
    
    console.log('Auth state changed:', event, 'User ID:', nhostSession?.user?.id, 'Email:', nhostSession?.user?.email);
    
    if (event === 'SIGNED_IN' && nhostSession?.user) {
      // Only set session for valid sign-in events
      session.set(nhostSession);
      // Start session management
      if (!sessionManager) {
        sessionManager = new SessionManager();
      }
      console.log('Session established for user:', nhostSession.user.email);
    } else if (event === 'SIGNED_OUT' || event === 'TOKEN_CHANGED' || !nhostSession?.user) {
      // CRITICAL: Only clear if not already in logout process
      if (!isSigningOut) {
        session.set(null);
        userProfile.set(null);
        sessionWarning.set(false);
        sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
        
        if (sessionManager) {
          sessionManager.destroy();
          sessionManager = null;
        }
        
        console.log('Session cleared due to:', event);
      }
    }
  });

  initializeAuth();
}

// Enhanced auth actions
export const authActions = {
  signUp: async (email: string, password: string, options: SignUpOptions = {}, turnstileToken?: string): Promise<AuthResult> => {
    console.log('🔄 Starting signUp with Turnstile:', { 
      email, 
      hasTurnstile: !!turnstileToken 
    });
    
    // Build request options for nHost
    const requestOptions: RequestOptions = {};
    if (turnstileToken) {
      requestOptions.headers = {
        'x-cf-turnstile-response': turnstileToken
      };
    }
    
    try {
      const result = await nhost.auth.signUp(
        {
          email,
          password,
          options: {
            redirectTo: '/welcome',
            ...options
          }
        },
        requestOptions
      );
      
      console.log('📡 nHost signUp response:', {
        hasSession: !!result.session,
        hasUser: !!result.session?.user,
        hasError: !!result.error,
        errorMessage: result.error?.message
      });
      
      return result as AuthResult;
    } catch (error) {
      console.error('❌ SignUp error caught:', error);
      return { error, session: null } as AuthResult;
    }
  },

  signIn: async (email: string, password: string): Promise<AuthResult> => {
    // Store device info for tracking (only in browser)
    let deviceInfo: DeviceInfo | Record<string, never> = {};
    if (browser && typeof navigator !== 'undefined') {
      deviceInfo = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        timestamp: new Date().toISOString()
      };
      // Log device info for tracking purposes
      console.log('Device info captured for sign-in:', deviceInfo);
    }

    const result = await nhost.auth.signIn({
      email,
      password
    });

    if (result.session?.user && browser) {
      // Track login
      console.log('User signed in successfully');
    }

    return result as AuthResult;
  },

  // Disable auth state listener (nuclear option)
  disableAuthListener: () => {
    console.log('🚫 DISABLING AUTH LISTENER');
    authStateListenerActive = false;
    isSigningOut = true;
  },

  // Re-enable auth state listener 
  enableAuthListener: () => {
    console.log('✅ ENABLING AUTH LISTENER');
    authStateListenerActive = true;
    isSigningOut = false;
  },

  signOut: async (): Promise<AuthResult> => {
    console.log('🚪 INITIATING IMMEDIATE SIGN OUT');
    
    // Prevent multiple signouts
    if (isSigningOut) {
      console.log('⚠️ Sign out already in progress, ignoring request');
      return { error: null, session: null } as AuthResult;
    }
    
    isSigningOut = true;
    
    // IMMEDIATE redirect to prevent UI race conditions
    if (browser) {
      console.log('🔄 Immediate redirect to prevent race conditions...');
      const timestamp = Date.now();
      window.location.href = `/?_t=${timestamp}`;
    }
    
    // Clean up in background (async, non-blocking)
    setTimeout(async () => {
      try {
        // Clear internal state
        session.set(null);
        userProfile.set(null);
        sessionWarning.set(false);
        sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
        
        if (sessionManager) {
          sessionManager.destroy();
          sessionManager = null;
        }
        
        // Clear browser storage
        if (browser) {
          localStorage.clear();
          sessionStorage.clear();
          
          // Clear cookies
          document.cookie.split(";").forEach(cookie => {
            const eqPos = cookie.indexOf("=");
            const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
            document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
          });
        }
        
        // Call nHost signOut in background
        await nhost.auth.signOut();
        
        console.log('✅ Background cleanup completed');
      } catch (error) {
        console.error('❌ Background cleanup error:', error);
      } finally {
        isSigningOut = false;
      }
    }, 0);
    
    return { error: null, session: null } as AuthResult;
  },

  // Extend current session (for warning dialog)
  extendSession: () => {
    if (sessionManager) {
      sessionManager.extendSession();
    }
  },

  // Force session termination
  forceLogout: async () => {
    session.set(null);
    userProfile.set(null);
    sessionWarning.set(false);
    
    if (sessionManager) {
      sessionManager.destroy();
      sessionManager = null;
    }
    
    if (browser) {
      try {
        // Clear ALL browser storage
        localStorage.clear();
        sessionStorage.clear();
        
        // Clear cookies
        document.cookie.split(";").forEach(cookie => {
          const eqPos = cookie.indexOf("=");
          const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        });
        
        // Clear IndexedDB
        if ('indexedDB' in window) {
          const databases = await indexedDB.databases();
          await Promise.all(
            databases.map(db => {
              if (db.name?.includes('nhost') || db.name?.includes('auth') || db.name?.includes('hasura')) {
                return new Promise<void>((resolve) => {
                  const deleteReq = indexedDB.deleteDatabase(db.name!);
                  deleteReq.onsuccess = () => resolve();
                  deleteReq.onerror = () => resolve();
                });
              }
              return Promise.resolve();
            })
          );
        }
      } catch (error) {
        console.error('Force logout storage clearing error:', error);
      }
      
      window.location.href = '/';
    }
  },

  // NUCLEAR OPTION: Complete session reset
  forceSessionReset: async () => {
    console.log('FORCE SESSION RESET - Nuclear option activated');
    
    if (browser) {
      try {
        // Clear everything aggressively
        localStorage.clear();
        sessionStorage.clear();
        
        // Clear all cookies
        document.cookie.split(";").forEach(cookie => {
          const eqPos = cookie.indexOf("=");
          const name = eqPos > -1 ? cookie.substring(0, eqPos) : cookie;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`;
        });
        
        // Nuclear option: Clear ALL IndexedDB databases
        if ('indexedDB' in window) {
          const databases = await indexedDB.databases();
          await Promise.all(
            databases.map(db => new Promise<void>((resolve) => {
              console.log('NUCLEAR: Deleting database:', db.name);
              const deleteReq = indexedDB.deleteDatabase(db.name!);
              deleteReq.onsuccess = () => resolve();
              deleteReq.onerror = () => resolve();
            }))
          );
        }
      } catch (error) {
        console.error('Force reset error:', error);
      }
    }
    
    // Clear all internal state
    session.set(null);
    userProfile.set(null);
    sessionWarning.set(false);
    
    if (sessionManager) {
      sessionManager.destroy();
      sessionManager = null;
    }
    
    // Multiple nHost signOut calls
    try {
      await nhost.auth.signOut();
      await nhost.auth.signOut(); // Double signOut
      
      // NUCLEAR: Recreate the entire nHost client
      recreateNhostClient();
    } catch (error) {
      console.error('Force nHost signOut error:', error);
    }
    
    if (browser) {
      // Force page reload to completely reset everything
      window.location.href = '/?force_reset=true';
    }
  },

  sendPasswordResetEmail: async (email: string): Promise<AuthResult> => {
    console.log('🔄 Sending password reset email to:', email);
    
    try {
      const result = await nhost.auth.resetPassword({ 
        email,
        options: {
          redirectTo: '/profile/settings?action=change-password'
        }
      });
      
      console.log('📧 Password reset response:', {
        hasError: !!result.error,
        errorMessage: result.error?.message,
        errorStatus: (result.error as NhostError)?.status
      });

      // Enhanced error handling for common scenarios
      if (result.error) {
        const errorMessage = result.error.message;
        const errorStatus = (result.error as NhostError)?.status;
        
        // Handle specific nHost error codes/messages
        if (errorStatus === 401 || errorMessage?.includes('unverified')) {
          return {
            session: null,
            error: {
              message: 'Please verify your email address before resetting your password.',
              code: 'USER_UNVERIFIED'
            }
          } as AuthResult;
        }
        
        if (errorStatus === 404 || errorMessage?.includes('not found')) {
          return {
            session: null,
            error: {
              message: 'No account found with this email address.',
              code: 'USER_NOT_FOUND'
            }
          } as AuthResult;
        }
      }
      
      return result as AuthResult;
    } catch (error) {
      console.error('❌ Password reset email error:', error);
      return {
        session: null,
        error: {
          message: 'Failed to send password reset email. Please try again.',
          code: 'NETWORK_ERROR'
        }
      } as AuthResult;
    }
  },

  resetPassword: async (password: string, passwordResetToken: string): Promise<AuthResult> => {
    console.log('🔄 Attempting password reset with:', {
      hasPassword: !!password,
      tokenLength: passwordResetToken?.length,
      tokenStart: passwordResetToken?.substring(0, 8) + '...'
    });
    
    try {
      // The passwordResetToken might be an expired refreshToken from nHost redirect
      // Let's try using it as both ticket and refreshToken, but with proper error handling
      
      console.log('🎫 Trying direct API call to nHost auth service...');
      
      // Try direct API call to nHost's change password endpoint
      const response = await fetch(`${nhost.auth.url}/user/password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          newPassword: password,
          ticket: passwordResetToken
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Direct API password reset successful');
        return { session: data.session || null, error: null } as AuthResult;
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.log('❌ Direct API failed:', response.status, errorData);
        
        // If it's a 401/400, the token is likely expired
        if (response.status === 401 || response.status === 400) {
          console.log('🔄 Token expired, trying alternative approach...');
          
          // Return a specific error that the frontend can handle
          return { 
            session: null, 
            error: { 
              message: 'Password reset link has expired. Please request a new reset link.',
              code: 'TOKEN_EXPIRED'
            } 
          } as AuthResult;
        }
        
        return { 
          session: null, 
          error: { message: errorData.message || 'Password reset failed' } 
        } as AuthResult;
      }
      
    } catch (error) {
      console.error('🚨 Password reset error:', error);
      return { 
        session: null, 
        error: { 
          message: 'Password reset link has expired. Please request a new reset link.',
          code: 'TOKEN_EXPIRED'
        } 
      } as AuthResult;
    }
  }
};

// Export session configuration for components
export { SESSION_CONFIG };
