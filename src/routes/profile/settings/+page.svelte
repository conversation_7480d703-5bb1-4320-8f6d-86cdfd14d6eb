<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';
  import AccountDeletionDialog from '$lib/components/dialogs/AccountDeletionDialog.svelte';
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import { nhost } from '$lib/stores/nhost.js';
  import { session, authActions } from '$lib/stores/auth.js';
  import toast from 'svelte-5-french-toast';
  import { Card, CardContent, CardHeader } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Eye, EyeOff, CheckCircle, X, Trash2 } from 'lucide-svelte';

  let newPassword = $state('');
  let confirmPassword = $state('');
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let isLoading = $state(false);
  let showChangePassword = $state(false);
  let passwordChangeSuccess = $state(false);
  let countdownSeconds = $state(5);
  let countdownInterval: any = $state(null);
  let signOutTimeout: any = $state(null);
  let showDeleteDialog = $state(false);

  onMount(() => {
    // ProtectedRoute will handle authentication
    // Just check for password reset action
    const action = $page.url.searchParams.get('action');
    if (action === 'change-password') {
      showChangePassword = true;
      toast.success('You have been signed in. Please set your new password below.');
    }
  });

  async function handlePasswordChange(event: Event) {
    event.preventDefault();
    
    if (!newPassword || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (newPassword.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    isLoading = true;

    try {
      const result = await nhost.auth.changePassword({ newPassword });
      
      if (result.error) {
        console.error('❌ Password change failed:', result.error);
        toast.error(result.error.message || 'Failed to change password');
        return;
      }

      console.log('✅ Password changed successfully');
      
      // Clear form fields
      newPassword = '';
      confirmPassword = '';
      passwordChangeSuccess = true;
      
      // Show success message with countdown
      toast.success('Password changed successfully! You will be signed out in 5 seconds for security.', {
        duration: 5000
      });
      
      // Start countdown
      countdownInterval = setInterval(() => {
        countdownSeconds--;
        if (countdownSeconds <= 0) {
          if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
          }
        }
      }, 1000);
      
      // Give user time to see the success message before signing out
      signOutTimeout = setTimeout(async () => {
        toast.success('Redirecting to sign in page...', { duration: 2000 });
        await authActions.signOut();
      }, 5000);
      
    } catch (error) {
      console.error('Password change error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
    }
  }

  function cancelAutoSignOut() {
    if (countdownInterval) {
      clearInterval(countdownInterval);
      countdownInterval = null;
    }
    if (signOutTimeout) {
      clearTimeout(signOutTimeout);
      signOutTimeout = null;
    }
    passwordChangeSuccess = false;
    countdownSeconds = 5; // Reset for next time
    toast.success('Auto sign-out cancelled. You can continue using the app.', { duration: 3000 });
  }

  async function handleAccountDeletion(data: { reason?: string }) {
    try {
      // Get current user session
      const currentSession = $session;
      if (!currentSession?.user?.id) {
        toast.error('No active session found. Please sign in again.');
        throw new Error('No active session');
      }

      console.log('Account deletion requested for user:', currentSession.user.id, 'with reason:', data.reason);
      
      // Make GraphQL request to mark account for deletion
      const response = await nhost.graphql.request(`
        mutation RequestAccountDeletion($userId: uuid!, $reason: String) {
          update_user_profiles(
            where: {user_id: {_eq: $userId}},
            _set: {
              deletion_requested_at: "now()",
              deletion_reason: $reason
            }
          ) {
            affected_rows
            returning {
              id
              deletion_requested_at
              deletion_reason
            }
          }
        }
      `, {
        userId: currentSession.user.id,
        reason: data.reason || null
      });

      if (response.error) {
        console.error('❌ Account deletion request failed:', response.error);
        toast.error(response.error.message || 'Failed to submit deletion request');
        throw response.error;
      }

      const result = response.data?.update_user_profiles;
      if (!result?.affected_rows || result.affected_rows === 0) {
        console.error('❌ No user profile found or updated');
        toast.error('Failed to update user profile. Please try again.');
        throw new Error('No rows affected');
      }

      console.log('✅ Account deletion request submitted successfully:', result.returning[0]);
      toast.success('Account deletion request submitted successfully. You will be contacted within 48 hours.');

      // Sign out after successful deletion request
      setTimeout(async () => {
        await authActions.signOut();
      }, 2000);
      
    } catch (error) {
      console.error('Account deletion failed:', error);
      toast.error('Failed to submit deletion request. Please try again.');
      throw error; // Re-throw to keep dialog open
    }
  }
</script>

<svelte:head>
  <title>Profile Settings - SourceFlex</title>
</svelte:head>

<ProtectedRoute>
  <AppLayout>
    <div class="container mx-auto max-w-2xl p-6">
      <h1 class="text-2xl font-bold mb-6">Profile Settings</h1>

  {#if showChangePassword}
    <!-- Password Reset Section -->
    <Card class="mb-6 border-blue-200 bg-blue-50">
      <CardHeader>
        <div class="flex items-center gap-2">
          <CheckCircle class="h-5 w-5 text-blue-600" />
          <h2 class="text-lg font-semibold text-blue-900">Change Your Password</h2>
        </div>
        <p class="text-sm text-blue-700">
          You've been signed in through a password reset link. Please set your new password below.
        </p>
      </CardHeader>
      <CardContent>
        <form onsubmit={handlePasswordChange} class="space-y-4">
          <!-- New Password Field -->
          <div class="form-group">
            <Label for="new-password">New Password</Label>
            <div class="password-wrapper relative">
              <Input
                id="new-password"
                type={showPassword ? 'text' : 'password'}
                bind:value={newPassword}
                placeholder="Enter new password"
                disabled={isLoading}
                required
                class="pr-10"
              />
              <button
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                onclick={() => showPassword = !showPassword}
              >
                {#if showPassword}
                  <EyeOff class="h-4 w-4" />
                {:else}
                  <Eye class="h-4 w-4" />
                {/if}
              </button>
            </div>
            <p class="text-xs text-muted-foreground mt-1">
              Must be at least 8 characters long
            </p>
          </div>

          <!-- Confirm Password Field -->
          <div class="form-group">
            <Label for="confirm-password">Confirm New Password</Label>
            <div class="password-wrapper relative">
              <Input
                id="confirm-password"
                type={showConfirmPassword ? 'text' : 'password'}
                bind:value={confirmPassword}
                placeholder="Confirm new password"
                disabled={isLoading}
                required
                class="pr-10"
              />
              <button
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                onclick={() => showConfirmPassword = !showConfirmPassword}
              >
                {#if showConfirmPassword}
                  <EyeOff class="h-4 w-4" />
                {:else}
                  <Eye class="h-4 w-4" />
                {/if}
              </button>
            </div>
          </div>

          <Button type="submit" class="w-full" disabled={isLoading}>
            {#if isLoading}
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Changing password...
            {:else}
              Change Password
            {/if}
          </Button>
        </form>
      </CardContent>
    </Card>
  {/if}

  {#if passwordChangeSuccess}
    <!-- Password Change Success Section -->
    <Card class="mb-6 border-green-200 bg-green-50">
      <CardHeader>
        <div class="flex items-center gap-2">
          <CheckCircle class="h-5 w-5 text-green-600" />
          <h2 class="text-lg font-semibold text-green-900">Password Changed Successfully!</h2>
        </div>
        <p class="text-sm text-green-700">
          Your password has been updated successfully. For security reasons, you will be signed out automatically.
        </p>
      </CardHeader>
      <CardContent>
        <div class="flex items-center justify-center p-4">
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">{countdownSeconds}</div>
            <p class="text-sm text-green-700 mb-3">
              Signing out in {countdownSeconds} second{countdownSeconds !== 1 ? 's' : ''}...
            </p>
            <p class="text-xs text-green-600 mb-4">
              You'll be redirected to the sign-in page to log in with your new password.
            </p>
            <Button 
              variant="outline" 
              size="sm"
              onclick={cancelAutoSignOut}
              class="border-green-600 text-green-700 hover:bg-green-100"
            >
              <X class="h-4 w-4 mr-2" />
              Stay Signed In
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  {/if}

  <!-- Other Settings (placeholder for future) -->
  <Card>
    <CardHeader>
      <h2 class="text-lg font-semibold">Account Settings</h2>
      <p class="text-sm text-muted-foreground">
        Manage your account preferences and security settings.
      </p>
    </CardHeader>
    <CardContent>
      <div class="space-y-4">
        <Button 
          variant="outline" 
          onclick={() => showChangePassword = !showChangePassword}
        >
          {showChangePassword ? 'Hide' : 'Show'} Change Password
        </Button>
        
        <div class="text-sm text-muted-foreground">
          More settings will be available here soon.
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- Danger Zone -->
  <Card class="border-red-100">
    <CardHeader class="pb-3">
      <h2 class="text-lg font-semibold text-red-900">Danger Zone</h2>
      <p class="text-sm text-muted-foreground">
        Irreversible actions that will permanently affect your account.
      </p>
    </CardHeader>
    <CardContent>
      <div class="rounded-lg border border-red-200 p-4">
        <div class="flex items-start justify-between">
          <div class="space-y-1">
            <h3 class="font-medium text-red-900">Delete Account</h3>
            <p class="text-sm text-muted-foreground">
              Permanently remove your account and all associated data.
            </p>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            class="border-red-300 text-red-700 hover:bg-red-50"
            onclick={() => showDeleteDialog = true}
          >
            <Trash2 class="h-4 w-4 mr-2" />
            Delete Account
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</div>
  </AppLayout>
</ProtectedRoute>

<!-- Account Deletion Dialog -->
<AccountDeletionDialog
  bind:open={showDeleteDialog}
  onOpenChange={(open) => showDeleteDialog = open}
  onConfirm={handleAccountDeletion}
/>

<style>
  .password-wrapper {
    position: relative;
  }
</style>