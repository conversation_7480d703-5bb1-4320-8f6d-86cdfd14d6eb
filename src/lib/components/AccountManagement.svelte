<script lang="ts">
  import { graphql } from '$houdini';
  import { onMount } from 'svelte';
  
  interface Props {
    userId: string;
  }
  
  let { userId }: Props = $props();
  
  // Use inline GraphQL with unique names
  const getUserProfileDetail = graphql(`
    query GetUserProfileDetail($userId: uuid!) {
      user_profiles(where: {user_id: {_eq: $userId}}) {
        id
        user_id
        current_desk
        current_organization_id
        email_domain
        company_name
        timezone
        onboarding_completed
        is_active
        created_at
        updated_at
        deleted_at
        deletion_reason
        deletion_requested_at
      }
    }
  `);
  
  const requestAccountDeletion = graphql(`
    mutation RequestAccountDeletionComponent($userId: uuid!, $reason: String) {
      update_user_profiles(
        where: {user_id: {_eq: $userId}},
        _set: {
          deletion_requested_at: "now()",
          deletion_reason: $reason
        }
      ) {
        affected_rows
        returning {
          id
          deletion_requested_at
          deletion_reason
        }
      }
    }
  `);
  
  const cancelAccountDeletion = graphql(`
    mutation CancelAccountDeletionComponent($userId: uuid!) {
      update_user_profiles(
        where: {user_id: {_eq: $userId}},
        _set: {
          deletion_requested_at: null,
          deletion_reason: null
        }
      ) {
        affected_rows
        returning {
          id
          deletion_requested_at
          deletion_reason
        }
      }
    }
  `);
  
  const processAccountDeletion = graphql(`
    mutation ProcessAccountDeletionComponent($userId: uuid!) {
      update_user_profiles(
        where: {user_id: {_eq: $userId}},
        _set: {
          deleted_at: "now()",
          is_active: false
        }
      ) {
        affected_rows
        returning {
          id
          deleted_at
          is_active
          deletion_requested_at
          deletion_reason
        }
      }
    }
  `);
  
  // State
  let deletionReason = $state('');
  let loading = $state(false);
  let message = $state('');
  
  // Reactive user profile
  let userProfile = $derived($getUserProfileDetail.data?.user_profiles?.[0]);
  
  onMount(() => {
    // Load user profile on mount
    getUserProfileDetail.fetch({ variables: { userId } });
  });
  
  // Action handlers
  async function handleRequestDeletion() {
    if (!deletionReason.trim()) {
      message = 'Please provide a reason for deletion';
      return;
    }
    
    loading = true;
    try {
      const result = await requestAccountDeletion.mutate({
        userId,
        reason: deletionReason
      });
      
      if (result.data?.update_user_profiles?.affected_rows) {
        message = 'Account deletion requested successfully';
        // Refresh user profile to show updated state
        await getUserProfileDetail.fetch({ variables: { userId } });
        deletionReason = '';
      }
    } catch (error) {
      message = `Error requesting deletion: ${error}`;
    } finally {
      loading = false;
    }
  }
  
  async function handleCancelDeletion() {
    loading = true;
    try {
      const result = await cancelAccountDeletion.mutate({ userId });
      
      if (result.data?.update_user_profiles?.affected_rows) {
        message = 'Account deletion request cancelled';
        // Refresh user profile
        await getUserProfileDetail.fetch({ variables: { userId } });
      }
    } catch (error) {
      message = `Error cancelling deletion: ${error}`;
    } finally {
      loading = false;
    }
  }
  
  async function handleProcessDeletion() {
    if (!confirm('Are you sure? This will soft-delete the account permanently.')) {
      return;
    }
    
    loading = true;
    try {
      const result = await processAccountDeletion.mutate({ userId });
      
      if (result.data?.update_user_profiles?.affected_rows) {
        message = 'Account has been deleted';
        // Refresh user profile
        await getUserProfileDetail.fetch({ variables: { userId } });
      }
    } catch (error) {
      message = `Error processing deletion: ${error}`;
    } finally {
      loading = false;
    }
  }
</script>

<div class="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
  <h2 class="text-2xl font-bold mb-4">Account Management</h2>
  
  {#if $getUserProfileDetail.fetching}
    <p class="text-gray-500">Loading user profile...</p>
  {:else if userProfile}
    <div class="space-y-4">
      <!-- User Info -->
      <div class="border-b pb-4">
        <h3 class="font-semibold">User Profile</h3>
        <p><strong>Email Domain:</strong> {userProfile.email_domain}</p>
        <p><strong>Company:</strong> {userProfile.company_name || 'Not set'}</p>
        <p><strong>Active:</strong> {userProfile.is_active ? 'Yes' : 'No'}</p>
        <p><strong>Onboarding:</strong> {userProfile.onboarding_completed ? 'Complete' : 'Pending'}</p>
      </div>
      
      <!-- Deletion Status -->
      <div class="border-b pb-4">
        <h3 class="font-semibold">Deletion Status</h3>
        
        {#if userProfile.deleted_at}
          <div class="text-red-600">
            <p><strong>Account Deleted:</strong> {new Date(userProfile.deleted_at).toLocaleString()}</p>
            {#if userProfile.deletion_reason}
              <p><strong>Reason:</strong> {userProfile.deletion_reason}</p>
            {/if}
          </div>
        {:else if userProfile.deletion_requested_at}
          <div class="text-yellow-600">
            <p><strong>Deletion Requested:</strong> {new Date(userProfile.deletion_requested_at).toLocaleString()}</p>
            {#if userProfile.deletion_reason}
              <p><strong>Reason:</strong> {userProfile.deletion_reason}</p>
            {/if}
          </div>
        {:else}
          <p class="text-green-600">Account is active with no deletion requests</p>
        {/if}
      </div>
      
      <!-- Actions -->
      <div class="space-y-3">
        {#if !userProfile.deleted_at}
          {#if !userProfile.deletion_requested_at}
            <!-- Request deletion -->
            <div>
              <label for="reason" class="block text-sm font-medium mb-1">Deletion Reason:</label>
              <textarea
                id="reason"
                bind:value={deletionReason}
                placeholder="Please provide a reason for account deletion..."
                class="w-full p-2 border rounded-md"
                rows="3"
              ></textarea>
              <button
                onclick={handleRequestDeletion}
                disabled={loading || !deletionReason.trim()}
                class="mt-2 w-full bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700 disabled:opacity-50"
              >
                {loading ? 'Processing...' : 'Request Account Deletion'}
              </button>
            </div>
          {:else}
            <!-- Cancel or process deletion -->
            <div class="space-y-2">
              <button
                onclick={handleCancelDeletion}
                disabled={loading}
                class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                {loading ? 'Processing...' : 'Cancel Deletion Request'}
              </button>
              
              <button
                onclick={handleProcessDeletion}
                disabled={loading}
                class="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                {loading ? 'Processing...' : 'Process Deletion (Admin Only)'}
              </button>
            </div>
          {/if}
        {:else}
          <p class="text-gray-500 italic">Account has been deleted</p>
        {/if}
      </div>
      
      <!-- Status Message -->
      {#if message}
        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p class="text-blue-800">{message}</p>
        </div>
      {/if}
    </div>
  {:else}
    <p class="text-red-500">User profile not found</p>
  {/if}
</div>
